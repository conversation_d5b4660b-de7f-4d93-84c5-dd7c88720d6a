//
//  AuthManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import Foundation
import SwiftUI

// MARK: - 认证管理器协议

/// 认证管理器协议，定义认证相关操作
protocol AuthManagerProtocol {
    /// 用户登录
    func login(userId: String, password: String) async throws -> AuthData

    /// 用户注册
    func register(userId: String, password: String) async throws -> AuthData

    /// 登出
    func logout() async

    /// 验证表单输入
    func validateCredentials(userId: String, password: String) -> ValidationResult
}

// MARK: - 认证管理器实现

/// 认证管理器，负责处理用户认证相关操作
@MainActor
class AuthManager: ObservableObject, AuthManagerProtocol {

    // MARK: - Published Properties

    /// 当前认证状态
    @Published var authState: AuthState = .notAuthenticated

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 登录状态存储
    @AppStorage("isLoggedIn") var isLoggedIn: Bool = false
    @AppStorage("currentUserId") var currentUserId: String = ""
    @AppStorage("authToken") var authToken: String = ""

    // MARK: - Private Properties

    private let urlSession: URLSession
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = AuthConfig.requestTimeout
        config.timeoutIntervalForResource = AuthConfig.requestTimeout
        self.urlSession = URLSession(configuration: config)

        // 配置JSON编码器
        jsonEncoder.dateEncodingStrategy = .iso8601
        jsonDecoder.dateDecodingStrategy = .iso8601

        // 检查本地登录状态
        checkLocalAuthState()
    }

    // MARK: - Public Methods

    /// 用户登录
    func login(userId: String, password: String) async throws -> AuthData {
        // 验证输入
        let validation = validateCredentials(userId: userId, password: password)
        guard validation.isValid else {
            throw AuthError.unknown(validation.errorMessage ?? "输入验证失败")
        }

        isLoading = true
        errorMessage = nil
        authState = .authenticating

        do {
            let request = LoginRequest(userId: userId, password: password)
            let authData = try await performAuthRequest(
                endpoint: AuthConfig.loginEndpoint,
                requestBody: request
            )

            // 保存认证信息
            await saveAuthData(authData)
            authState = .authenticated(authData)

            return authData
        } catch {
            let authError = mapError(error)
            errorMessage = authError.localizedDescription
            authState = .error(authError)
            throw authError
        }
    }

    /// 用户注册
    func register(userId: String, password: String) async throws -> AuthData {
        // 验证输入
        let validation = validateCredentials(userId: userId, password: password)
        guard validation.isValid else {
            throw AuthError.unknown(validation.errorMessage ?? "输入验证失败")
        }

        isLoading = true
        errorMessage = nil
        authState = .authenticating

        do {
            let request = RegisterRequest(userId: userId, password: password)
            let authData = try await performAuthRequest(
                endpoint: AuthConfig.registerEndpoint,
                requestBody: request
            )

            // 保存认证信息
            await saveAuthData(authData)
            authState = .authenticated(authData)

            return authData
        } catch {
            let authError = mapError(error)
            errorMessage = authError.localizedDescription
            authState = .error(authError)
            throw authError
        }
    }

    /// 登出
    func logout() async {
        isLoading = true

        // 清除本地存储的认证信息
        isLoggedIn = false
        currentUserId = ""
        authToken = ""

        // 重置状态
        authState = .notAuthenticated
        errorMessage = nil
        isLoading = false
    }

    /// 验证表单输入
    nonisolated func validateCredentials(userId: String, password: String) -> ValidationResult {
        // 验证用户ID长度
        if userId.count < AuthConfig.minUserIdLength {
            return .invalid("用户ID至少需要\(AuthConfig.minUserIdLength)位字符")
        }

        // 验证密码长度
        if password.count < AuthConfig.minPasswordLength {
            return .invalid("密码至少需要\(AuthConfig.minPasswordLength)位字符")
        }

        return .valid
    }

    // MARK: - Private Methods

    /// 执行认证请求
    private func performAuthRequest<T: Codable>(
        endpoint: String,
        requestBody: T
    ) async throws -> AuthData {
        // 构建URL
        guard let url = URL(string: AuthConfig.baseURL + endpoint) else {
            throw AuthError.invalidResponse
        }

        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // 编码请求体
        do {
            request.httpBody = try jsonEncoder.encode(requestBody)
        } catch {
            throw AuthError.invalidResponse
        }

        // 执行请求
        do {
            let (data, response) = try await urlSession.data(for: request)

            // 检查HTTP状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                throw AuthError.networkError
            }

            // 解析响应
            let authResponse = try jsonDecoder.decode(AuthResponse.self, from: data)

            // 处理业务逻辑错误
            if !authResponse.success {
                if let errorMessage = authResponse.error {
                    throw mapServerError(errorMessage, statusCode: httpResponse.statusCode)
                } else {
                    throw AuthError.serverError
                }
            }

            // 返回认证数据
            guard let authData = authResponse.data else {
                throw AuthError.invalidResponse
            }

            return authData

        } catch let error as AuthError {
            throw error
        } catch {
            throw AuthError.networkError
        }
    }

    /// 保存认证数据到本地
    private func saveAuthData(_ authData: AuthData) async {
        isLoggedIn = true
        currentUserId = authData.userId
        authToken = authData.token ?? ""
        isLoading = false
    }

    /// 检查本地认证状态
    private func checkLocalAuthState() {
        if isLoggedIn && !currentUserId.isEmpty {
            let authData = AuthData(
                userId: currentUserId,
                token: authToken.isEmpty ? nil : authToken,
                refreshToken: nil,
                expiresAt: nil
            )
            authState = .authenticated(authData)
        } else {
            authState = .notAuthenticated
        }
    }

    /// 映射错误类型
    private func mapError(_ error: Error) -> AuthError {
        if let authError = error as? AuthError {
            return authError
        }

        if error is URLError {
            return .networkError
        }

        return .unknown(error.localizedDescription)
    }

    /// 映射服务器错误
    private func mapServerError(_ errorMessage: String, statusCode: Int) -> AuthError {
        switch statusCode {
        case 400:
            if errorMessage.contains("already exists") || errorMessage.contains("已存在") {
                return .userAlreadyExists
            } else if errorMessage.contains("not found") || errorMessage.contains("不存在") {
                return .userNotFound
            } else {
                return .invalidCredentials
            }
        case 401:
            return .invalidCredentials
        case 404:
            return .userNotFound
        case 500...599:
            return .serverError
        default:
            return .unknown(errorMessage)
        }
    }
}
