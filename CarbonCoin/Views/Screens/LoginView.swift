//
//  LoginView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import SwiftUI

struct LoginView: View {
    @State private var username: String = ""
    @State private var password: String = ""
    @State private var isPasswordVisible: Bool = false
    @State private var isLoading: Bool = false
    @State private var loginError: String?

    var body: some View {
        NavigationView {
            VStack {
                Form {
                    Section(header: Text("账号登录")) {
                        TextField("请输入用户ID", text: $username)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.none)
                            .disableAutocorrection(true)

                        HStack {
                            if isPasswordVisible {
                                TextField("请输入密码", text: $password)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            } else {
                                SecureField("请输入密码", text: $password)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                            But<PERSON>(action: {
                                isPasswordVisible.toggle()
                            }) {
                                Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                                    .foregroundColor(.gray)
                            }
                        }
                    }

                    Section {
                        Button(action: {
                            login()
                        }) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle())
                                } else {
                                    Text("登录")
                                }
                            }
                            .frame(maxWidth: .infinity, alignment: .center)
                        }
                        .disabled(isLoading || username.isEmpty || password.isEmpty)
                    }

                    if let error = loginError {
                        Section {
                            Text(error)
                                .foregroundColor(.red)
                                .font(.footnote)
                        }
                    }
                }
                .navigationTitle("登录")
            }
        }
    }

    private func login() {
        isLoading = true
        loginError = nil

        // 模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isLoading = false
            if username == "test" && password == "password" {
                // 登录成功
                // 在这里处理登录成功后的逻辑，例如跳转到主界面
            } else {
                // 登录失败
                loginError = "用户ID或密码错误"
            }
        }
    }
}

struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView()
    }
}

#Preview {
    LoginView()
}
